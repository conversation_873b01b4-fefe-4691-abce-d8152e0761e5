//
//  RouterManager.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

// Enum Types for distinguishing general and tab routes
enum RoutesType: String, Hashable, CaseIterable {
    case generalRoute, homeRoute, medicationsRoute, centerRoute, prescriptionsRoute, settingsRoute
}

// Navigation operation types for batch operations
enum NavigationOperation {
    case push(Route, RoutesType)
    case pop(RoutesType)
    case popToRoot(RoutesType)
    case replace([Route], RoutesType)
}

// Navigation errors for better error handling
enum NavigationError: Error, LocalizedError {
    case routeAlreadyExists(Route, RoutesType)
    case routeNotFound(String, RoutesType)
    case invalidRouteStack(RoutesType)
    case emptyStack(RoutesType)

    var errorDescription: String? {
        switch self {
        case .routeAlreadyExists(let route, let routeType):
            return "Route '\(route.rawValue)' already exists in \(routeType.displayName)"
        case .routeNotFound(let routeId, let routeType):
            return "Route '\(routeId)' not found in \(routeType.displayName)"
        case .invalidRouteStack(let routeType):
            return "Invalid route stack for \(routeType.displayName)"
        case .emptyStack(let routeType):
            return "Cannot navigate back from empty stack in \(routeType.displayName)"
        }
    }
}

@Observable
final class RouterManager {
    // MARK: - Optimized Route Management
    // Dictionary-based storage for O(1) access instead of individual arrays
    private var routeStacks: [RoutesType: [Route]] = [:]
    // Set-based cache for O(1) duplicate detection
    private var routeCache: [RoutesType: Set<Route>] = [:]

    // MARK: - Memory Management
    // Route access tracking for cleanup
    private var routeAccessTimes: [RoutesType: [Route: Date]] = [:]
    // Maximum routes per stack to prevent memory bloat
    private let maxRoutesPerStack: Int = 50
    // Cleanup timer for automatic memory management
    private var cleanupTimer: Timer?

    // MARK: - Performance Monitoring
    // Navigation performance metrics
    private var navigationMetrics: [String: TimeInterval] = [:]
    private var operationCounts: [String: Int] = [:]
    private let performanceThreshold: TimeInterval = 0.1 // 100ms

    // MARK: - Route Preloading
    // Critical routes that should be preloaded
    private let criticalRoutes: [Route] = [.dashboard, .login]
    // Preloaded route cache
    private var preloadedRoutes: Set<Route> = []

    // MARK: - Singleton Preloading Control
    // Global flag to ensure preloading only happens once across all instances
    private static var hasPreloadedCriticalRoutes = false
    private static let preloadingQueue = DispatchQueue(label: "com.wasfaadmin.routerpreloading", qos: .userInitiated)

    // MARK: - Legacy Computed Properties for Backward Compatibility
    // These maintain API compatibility while using optimized storage
    var generalRouteList: [Route] {
        get { routeStacks[.generalRoute] ?? [] }
        set { updateRouteStack(.generalRoute, newValue) }
    }

    var homeRouteList: [Route] {
        get { routeStacks[.homeRoute] ?? [] }
        set { updateRouteStack(.homeRoute, newValue) }
    }

    var medicationsRouteList: [Route] {
        get { routeStacks[.medicationsRoute] ?? [] }
        set { updateRouteStack(.medicationsRoute, newValue) }
    }

    var centerRouteList: [Route] {
        get { routeStacks[.centerRoute] ?? [] }
        set { updateRouteStack(.centerRoute, newValue) }
    }

    var prescriptionsRouteList: [Route] {
        get { routeStacks[.prescriptionsRoute] ?? [] }
        set { updateRouteStack(.prescriptionsRoute, newValue) }
    }

    var settingsRouteList: [Route] {
        get { routeStacks[.settingsRoute] ?? [] }
        set { updateRouteStack(.settingsRoute, newValue) }
    }

    init() {
        // Initialize all route types with empty stacks and caches
        RoutesType.allCases.forEach { routeType in
            routeStacks[routeType] = []
            routeCache[routeType] = Set<Route>()
            routeAccessTimes[routeType] = [:]
        }

        // Start automatic cleanup timer (runs every 5 minutes)
        startCleanupTimer()

        // Preload critical routes only once globally
        Task { @MainActor in
            await preloadCriticalRoutesOnce()
        }
    }

    deinit {
        cleanupTimer?.invalidate()
    }

    // MARK: - Private Helper Methods
    private func updateRouteStack(_ routeType: RoutesType, _ newStack: [Route]) {
        routeStacks[routeType] = newStack
        routeCache[routeType] = Set(newStack)
    }

    // MARK: - Optimized Navigation Methods

    /// Pushes a new screen to the specified route stack with O(1) duplicate detection
    /// - Parameters:
    ///   - screen: The route to push
    ///   - appState: Optional app state for automatic route type detection
    ///   - routesType: The route stack to push to (defaults to .generalRoute)
    ///   - forcefully: Whether to allow duplicate routes (defaults to true)
    @MainActor
    func push(to screen: Route, appState: AppState? = nil, where routesType: RoutesType = .generalRoute, forcefully: Bool = true) {
        let targetRouteType = appState.map(mapRouterWithTab) ?? routesType

        // O(1) duplicate check using Set instead of O(n) array.contains()
        if !forcefully && routeCache[targetRouteType]?.contains(screen) == true {
            print("⚠️ Route '\(screen.rawValue)' already exists in \(targetRouteType.rawValue)")
            return
        }

        // O(1) append and cache update
        routeStacks[targetRouteType]?.append(screen)
        routeCache[targetRouteType]?.insert(screen)
    }

    /// Navigates back to the previous screen in the specified route stack
    /// - Parameters:
    ///   - appState: Optional app state for automatic route type detection
    ///   - routesType: The route stack to pop from (defaults to .generalRoute)
    @MainActor
    func goBack(appState: AppState? = nil, where routesType: RoutesType = .generalRoute) {
        let targetRouteType = appState.map(mapRouterWithTab) ?? routesType

        // O(1) pop operation with cache synchronization
        if let removedRoute = routeStacks[targetRouteType]?.popLast() {
            routeCache[targetRouteType]?.remove(removedRoute)
        }
    }

    /// Replaces the entire route stack with a new array of routes
    /// - Parameters:
    ///   - stack: The new route stack to set
    ///   - routesType: The route stack to replace (defaults to .generalRoute)
    @MainActor
    func replace(stack: [Route], where routesType: RoutesType = .generalRoute) {
        routeStacks[routesType] = stack
        routeCache[routesType] = Set(stack)
    }

    /// Pops back to a specific screen in the route stack
    /// - Parameters:
    ///   - screen: The screen identifier to pop back to
    ///   - routesType: The route stack to operate on (defaults to .generalRoute)
    @MainActor
    func popToRout(to screen: String, where routesType: RoutesType = .generalRoute) {
        guard let stack = routeStacks[routesType] else { return }

        // Find the target route index
        guard let targetIndex = stack.firstIndex(where: { $0.rawValue == screen }) else {
            print("⚠️ Route '\(screen)' not found in \(routesType.rawValue)")
            return
        }

        // Keep routes up to and including the target route
        let newStack = Array(stack.prefix(targetIndex + 1))

        // Update both stack and cache efficiently
        routeStacks[routesType] = newStack
        routeCache[routesType] = Set(newStack)
    }


    /// Clears all route stacks across all route types
    @MainActor
    func popToAllRoot() {
        RoutesType.allCases.forEach { routeType in
            routeStacks[routeType] = []
            routeCache[routeType] = Set<Route>()
        }
    }

    /// Clears all tab-specific route stacks (excludes general routes)
    @MainActor
    func popToRootTabs() {
        let tabRouteTypes: [RoutesType] = [.homeRoute, .medicationsRoute, .centerRoute, .prescriptionsRoute, .settingsRoute]
        tabRouteTypes.forEach { routeType in
            routeStacks[routeType] = []
            routeCache[routeType] = Set<Route>()
        }
    }

    /// Pops all routes in the specified stack back to root
    /// - Parameter routesType: The route stack to clear (defaults to .generalRoute)
    @MainActor
    func popToRoot(where routesType: RoutesType = .generalRoute) {
        routeStacks[routesType] = []
        routeCache[routesType] = Set<Route>()
    }

    /// Checks if any tab route stack contains the specified route using O(1) cache lookup
    /// - Parameter route: The route to search for
    /// - Returns: True if the route exists in any tab stack
    func checkIfRouteContain(route: Route) -> Bool {
        let tabRouteTypes: [RoutesType] = [.homeRoute, .medicationsRoute, .centerRoute, .prescriptionsRoute, .settingsRoute]
        return tabRouteTypes.contains { routeType in
            routeCache[routeType]?.contains(route) == true
        }
    }

    // MARK: - Route Type Mapping

    /// Maps a Tab to its corresponding RoutesType
    /// - Parameter tab: The tab to map
    /// - Returns: The corresponding RoutesType
    func mapTabTypeToRoutesType(from tab: DoctorTab) -> RoutesType {
        tab.routeType
    }

    /// Maps AppState's selected tab to its corresponding RoutesType
    /// - Parameter appState: The app state containing the selected tab
    /// - Returns: The corresponding RoutesType
    func mapRouterWithTab(appState: AppState) -> RoutesType {
        appState.selectedTab.routeType
    }

    // MARK: - Utility Methods

    /// Gets a binding to the route stack for the specified route type
    /// - Parameter routeType: The route type to get the binding for
    /// - Returns: A binding to the route stack
    func getRouteStackBinding(for routeType: RoutesType) -> Binding<[Route]> {
        Binding(
            get: { self.routeStacks[routeType] ?? [] },
            set: { self.updateRouteStack(routeType, $0) }
        )
    }

    /// Gets the current route count for a specific route type
    /// - Parameter routeType: The route type to check
    /// - Returns: The number of routes in the stack
    func getRouteCount(for routeType: RoutesType) -> Int {
        routeStacks[routeType]?.count ?? 0
    }

    /// Checks if a specific route type stack is empty
    /// - Parameter routeType: The route type to check
    /// - Returns: True if the stack is empty
    func isEmpty(routeType: RoutesType) -> Bool {
        routeStacks[routeType]?.isEmpty ?? true
    }

    // MARK: - Async Navigation Methods

    /// Async version of push for better integration with modern Swift concurrency
    /// - Parameters:
    ///   - screen: The route to push
    ///   - appState: Optional app state for automatic route type detection
    ///   - routesType: The route stack to push to (defaults to .generalRoute)
    ///   - forcefully: Whether to allow duplicate routes (defaults to true)
    ///   - animated: Whether to animate the navigation (defaults to true)
    @MainActor
    func pushAsync(to screen: Route, appState: AppState? = nil, where routesType: RoutesType = .generalRoute, forcefully: Bool = true, animated: Bool = true) async {
        if animated {
            await withAnimation(.easeInOut(duration: 0.3)) {
                push(to: screen, appState: appState, where: routesType, forcefully: forcefully)
            }
        } else {
            push(to: screen, appState: appState, where: routesType, forcefully: forcefully)
        }
    }

    /// Async version of goBack with optional animation
    /// - Parameters:
    ///   - appState: Optional app state for automatic route type detection
    ///   - routesType: The route stack to pop from (defaults to .generalRoute)
    ///   - animated: Whether to animate the navigation (defaults to true)
    @MainActor
    func goBackAsync(appState: AppState? = nil, where routesType: RoutesType = .generalRoute, animated: Bool = true) async {
        if animated {
            await withAnimation(.easeInOut(duration: 0.3)) {
                goBack(appState: appState, where: routesType)
            }
        } else {
            goBack(appState: appState, where: routesType)
        }
    }

    /// Async version of popToRoot with optional animation
    /// - Parameters:
    ///   - routesType: The route stack to clear (defaults to .generalRoute)
    ///   - animated: Whether to animate the navigation (defaults to true)
    @MainActor
    func popToRootAsync(where routesType: RoutesType = .generalRoute, animated: Bool = true) async {
        if animated {
            await withAnimation(.easeInOut(duration: 0.3)) {
                popToRoot(where: routesType)
            }
        } else {
            popToRoot(where: routesType)
        }
    }

    // MARK: - Advanced Animation Support

    /// Navigation with custom animation
    /// - Parameters:
    ///   - screen: The route to push
    ///   - animation: Custom animation to use
    ///   - routesType: The route stack to push to
    @MainActor
    func pushWithAnimation(to screen: Route, animation: Animation = .easeInOut(duration: 0.3), where routesType: RoutesType = .generalRoute) async {
        await withAnimation(animation) {
            push(to: screen, where: routesType)
        }
    }

    /// Batch navigation operations with single animation
    /// - Parameters:
    ///   - operations: Array of navigation operations to perform
    ///   - animation: Animation to use for all operations
    @MainActor
    func performBatchNavigation(operations: [NavigationOperation], animation: Animation = .easeInOut(duration: 0.3)) async {
        await withAnimation(animation) {
            for operation in operations {
                switch operation {
                case .push(let route, let routeType):
                    push(to: route, where: routeType)
                case .pop(let routeType):
                    goBack(where: routeType)
                case .popToRoot(let routeType):
                    popToRoot(where: routeType)
                case .replace(let stack, let routeType):
                    replace(stack: stack, where: routeType)
                }
            }
        }
    }

    // MARK: - Error-Safe Navigation Methods

    /// Throws an error if navigation fails instead of silent failure
    /// - Parameters:
    ///   - screen: The route to push
    ///   - routesType: The route stack to push to
    ///   - forcefully: Whether to allow duplicate routes
    @MainActor
    func pushSafe(to screen: Route, where routesType: RoutesType = .generalRoute, forcefully: Bool = false) throws {
        if !forcefully && routeCache[routesType]?.contains(screen) == true {
            throw NavigationError.routeAlreadyExists(screen, routesType)
        }

        guard routeStacks[routesType] != nil else {
            throw NavigationError.invalidRouteStack(routesType)
        }

        routeStacks[routesType]?.append(screen)
        routeCache[routesType]?.insert(screen)
    }

    /// Safe version of goBack that throws if stack is empty
    /// - Parameter routesType: The route stack to pop from
    @MainActor
    func goBackSafe(where routesType: RoutesType = .generalRoute) throws {
        guard let stack = routeStacks[routesType], !stack.isEmpty else {
            throw NavigationError.emptyStack(routesType)
        }

        if let removedRoute = routeStacks[routesType]?.popLast() {
            routeCache[routesType]?.remove(removedRoute)
        }
    }

    /// Safe version of popToRoute that throws if route not found
    /// - Parameters:
    ///   - screen: The screen identifier to pop back to
    ///   - routesType: The route stack to operate on
    @MainActor
    func popToRouteSafe(to screen: String, where routesType: RoutesType = .generalRoute) throws {
        guard let stack = routeStacks[routesType] else {
            throw NavigationError.invalidRouteStack(routesType)
        }

        guard let targetIndex = stack.firstIndex(where: { $0.rawValue == screen }) else {
            throw NavigationError.routeNotFound(screen, routesType)
        }

        let newStack = Array(stack.prefix(targetIndex + 1))
        routeStacks[routesType] = newStack
        routeCache[routesType] = Set(newStack)
    }

    // MARK: - Result-Based Navigation Methods

    /// Returns Result type for navigation operations
    /// - Parameters:
    ///   - screen: The route to push
    ///   - routesType: The route stack to push to
    ///   - forcefully: Whether to allow duplicate routes
    @MainActor
    func pushWithResult(to screen: Route, where routesType: RoutesType = .generalRoute, forcefully: Bool = false) -> Result<Void, NavigationError> {
        do {
            try pushSafe(to: screen, where: routesType, forcefully: forcefully)
            return .success(())
        } catch let error as NavigationError {
            return .failure(error)
        } catch {
            return .failure(.invalidRouteStack(routesType))
        }
    }

    // MARK: - Memory Management Methods

    /// Starts the automatic cleanup timer
    private func startCleanupTimer() {
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performAutomaticCleanup()
            }
        }
    }

    /// Performs automatic cleanup of old routes
    @MainActor
    func performAutomaticCleanup() async {
        let cutoffTime = Date().addingTimeInterval(-1800) // 30 minutes ago

        for routeType in RoutesType.allCases {
            // Clean up old access times
            if var accessTimes = routeAccessTimes[routeType] {
                accessTimes = accessTimes.filter { _, accessTime in
                    accessTime >= cutoffTime
                }
                routeAccessTimes[routeType] = accessTimes
            }

            // Limit stack size if it exceeds maximum
            if let stack = routeStacks[routeType], stack.count > maxRoutesPerStack {
                let newStack = Array(stack.suffix(maxRoutesPerStack))
                routeStacks[routeType] = newStack
                routeCache[routeType] = Set(newStack)

                print("🧹 Cleaned up \(routeType.displayName) stack: \(stack.count) -> \(newStack.count) routes")
            }
        }
    }

    /// Manually trigger cleanup for a specific route type
    @MainActor
    func cleanupRouteStack(_ routeType: RoutesType) {
        guard let stack = routeStacks[routeType], stack.count > maxRoutesPerStack else { return }

        let newStack = Array(stack.suffix(maxRoutesPerStack))
        routeStacks[routeType] = newStack
        routeCache[routeType] = Set(newStack)
        routeAccessTimes[routeType] = [:]

        print("🧹 Manually cleaned up \(routeType.displayName) stack: \(stack.count) -> \(newStack.count) routes")
    }

    /// Get memory usage statistics
    func getMemoryStats() -> [RoutesType: Int] {
        var stats: [RoutesType: Int] = [:]
        for routeType in RoutesType.allCases {
            stats[routeType] = routeStacks[routeType]?.count ?? 0
        }
        return stats
    }

    /// Track route access for cleanup optimization
    private func trackRouteAccess(_ route: Route, in routeType: RoutesType) {
        routeAccessTimes[routeType]?[route] = Date()
    }

    // MARK: - Performance Monitoring Methods

    /// Tracks navigation performance for optimization insights
    /// - Parameters:
    ///   - operation: The operation name to track
    ///   - block: The operation to execute and measure
    @MainActor
    func trackNavigationPerformance<T>(operation: String, _ block: () async throws -> T) async rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
            navigationMetrics[operation] = timeElapsed
            operationCounts[operation, default: 0] += 1

            if timeElapsed > performanceThreshold {
                print("⚠️ Slow navigation operation: \(operation) took \(String(format: "%.3f", timeElapsed))s")
            }
        }
        return try await block()
    }

    /// Gets performance statistics for all navigation operations
    func getPerformanceStats() -> (metrics: [String: TimeInterval], counts: [String: Int]) {
        return (navigationMetrics, operationCounts)
    }

    /// Resets performance tracking data
    func resetPerformanceStats() {
        navigationMetrics.removeAll()
        operationCounts.removeAll()
    }

    /// Gets average performance for an operation
    func getAveragePerformance(for operation: String) -> TimeInterval? {
        guard let totalTime = navigationMetrics[operation],
              let count = operationCounts[operation],
              count > 0 else { return nil }
        return totalTime / TimeInterval(count)
    }

    /// Logs current performance summary
    func logPerformanceSummary() {
        print("📊 Navigation Performance Summary:")
        for (operation, time) in navigationMetrics.sorted(by: { $0.value > $1.value }) {
            let count = operationCounts[operation] ?? 1
            let avgTime = time / TimeInterval(count)
            print("  \(operation): \(String(format: "%.3f", avgTime))s avg (\(count) calls)")
        }
    }

    // MARK: - Route Preloading Methods

    /// Preloads critical routes only once globally across all RouterManager instances
    @MainActor
    private func preloadCriticalRoutesOnce() async {
        // Use async-safe approach with DispatchQueue
        let shouldPreload = await withCheckedContinuation { continuation in
            Self.preloadingQueue.async {
                let shouldPreload = !Self.hasPreloadedCriticalRoutes
                if shouldPreload {
                    Self.hasPreloadedCriticalRoutes = true
                }
                continuation.resume(returning: shouldPreload)
            }
        }

        // Check if preloading has already been done
        guard shouldPreload else {
            print("🔄 Critical routes already preloaded, skipping...")
            return
        }

        print("🚀 Starting critical route preloading...")

        await trackNavigationPerformance(operation: "preload_critical_routes") {
            await withTaskGroup(of: Void.self) { group in
                for route in criticalRoutes {
                    group.addTask {
                        await self.preloadRoute(route)
                    }
                }
            }
        }

        print("✅ Critical route preloading completed")
    }

    /// Preloads critical routes for better performance (public interface)
    @MainActor
    func preloadCriticalRoutes() async {
        await preloadCriticalRoutesOnce()
    }

    /// Preloads a specific route and its dependencies
    /// - Parameter route: The route to preload
    @MainActor
    private func preloadRoute(_ route: Route) async {
        guard !preloadedRoutes.contains(route) else { return }

        // Simulate preloading heavy dependencies
        // In a real app, this might load data, images, or other resources
        try? await Task.sleep(nanoseconds: 10_000_000) // 10ms simulation

        preloadedRoutes.insert(route)
        print("🚀 Preloaded route: \(route.rawValue)")
    }

    /// Checks if a route has been preloaded
    /// - Parameter route: The route to check
    /// - Returns: True if the route is preloaded
    func isRoutePreloaded(_ route: Route) -> Bool {
        preloadedRoutes.contains(route)
    }

    /// Preloads routes based on user navigation patterns
    /// - Parameter recentRoutes: Recently accessed routes to base predictions on
    @MainActor
    func preloadPredictedRoutes(based recentRoutes: [Route]) async {
        // Simple prediction: preload routes that are commonly accessed after current routes
        let predictedRoutes = getPredictedRoutes(from: recentRoutes)

        await withTaskGroup(of: Void.self) { group in
            for route in predictedRoutes {
                group.addTask {
                    await self.preloadRoute(route)
                }
            }
        }
    }

    /// Gets predicted routes based on navigation patterns
    /// - Parameter recentRoutes: Recent navigation history
    /// - Returns: Array of predicted routes to preload
    private func getPredictedRoutes(from recentRoutes: [Route]) -> [Route] {
        // Simple prediction logic - in a real app this could use ML or analytics
        var predicted: [Route] = []

        for route in recentRoutes {
            switch route {
            case .login:
                predicted.append(.dashboard)
            case .dashboard:
                // Predict user might go to any tab
                predicted.append(contentsOf: [.splash]) // Add other common routes
            default:
                break
            }
        }

        return Array(Set(predicted)) // Remove duplicates
    }

    /// Clears preloaded route cache
    func clearPreloadCache() {
        preloadedRoutes.removeAll()
        print("🧹 Cleared route preload cache")
    }

    /// Resets the global preloading state (for testing purposes)
    static func resetPreloadingState() {
        preloadingQueue.sync {
            hasPreloadedCriticalRoutes = false
        }
        print("🔄 Reset global preloading state")
    }
}

// MARK: - Extensions

extension DoctorTab {
    /// Maps Tab cases to their corresponding RoutesType
    var routeType: RoutesType {
        switch self {
        case .home: .homeRoute
        case .medications: .medicationsRoute
        case .center: .centerRoute
        case .prescriptions: .prescriptionsRoute
        case .settings: .settingsRoute
        }
    }
}

extension RoutesType {
    /// Returns whether this route type is a tab-specific route
    var isTabRoute: Bool {
        self != .generalRoute
    }

    /// Returns the display name for the route type
    var displayName: String {
        switch self {
        case .generalRoute: "General"
        case .homeRoute: "Home"
        case .medicationsRoute: "Medications"
        case .centerRoute: "Center"
        case .prescriptionsRoute: "Prescriptions"
        case .settingsRoute: "Settings"
        }
    }
}

struct EnvironmentModifier: ViewModifier {
    @StateObject private var appState = AppState()
    @State private var routerManager = RouterManager()

    func body(content: Content) -> some View {
        content
            .environment(\.colorScheme, .light)
            .environment(\.routerManager, routerManager)
            .environmentObject(appState)
            .environment(\.locale, appState.appLocale.locale)
            .environment(\.layoutDirection, appState.appLocale.direction)
            .onDisappear {
                // Cleanup when view disappears to prevent memory leaks
                Task { @MainActor in
                    await routerManager.performAutomaticCleanup()
                }
            }
    }
}

// Optimized Environment Modifier that prevents retain cycles
struct OptimizedEnvironmentModifier: ViewModifier {
    @StateObject private var appState = AppState()
    @State private var routerManager = RouterManager()

    func body(content: Content) -> some View {
        content
            .preferredColorScheme(.light)
            .environment(\.routerManager, routerManager)
            .environmentObject(appState)
            .environment(\.locale, appState.appLocale.locale)
            .environment(\.layoutDirection, appState.appLocale.direction)
            .onAppear {
                // Log performance stats on appear
                routerManager.logPerformanceSummary()
            }
            .onDisappear {
                // Aggressive cleanup when view disappears
                Task { @MainActor in
                    await routerManager.performAutomaticCleanup()
                    // Clean up all tab routes when app goes to background
                    routerManager.popToRootTabs()
                }
            }
    }
}

extension View {
    func attachAllEnvironmentObjects() -> some View {
        modifier(EnvironmentModifier())
    }
}

struct AppStateKey: EnvironmentKey {
    // Default value for the environment key
    static let defaultValue: AppState = .init()
}

struct RouterManagerKey: EnvironmentKey {
    // Shared default instance to prevent multiple initializations
    static let defaultValue: RouterManager = {
        let manager = RouterManager()
        print("⚠️ Using RouterManager default value - ensure proper environment setup")
        return manager
    }()
}

extension EnvironmentValues {
    var appState: AppState {
        get { self[AppStateKey.self] }
        set { self[AppStateKey.self] = newValue }
    }

    var routerManager: RouterManager {
        get { self[RouterManagerKey.self] }
        set { self[RouterManagerKey.self] = newValue }
    }
}
