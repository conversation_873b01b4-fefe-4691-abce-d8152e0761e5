//
//  AdminHomeView.swift
//  WasfaAdmin
//
//  Created by Apple on 13/08/2025.
//

import SwiftUI

struct AdminHomeView: View {
    @StateObject private var viewModel = AdminHomeViewModel()
    
    var body: some View {
        SuperView(viewModel: viewModel) {
            ScrollView {
                VStack(spacing: 32) {
                    // MARK: - Admin Analytics Card
                    
                    AdminAnalyticsCardSection()
                    
                    // MARK: - Sales Overview Section
                    
                    AdminSalesOverviewSection()
                    
                    // MARK: - Customer Insights Section
                    
                    AdminCustomerInsightsSection()
                    
                    // MARK: - Recent Orders Section
                    
                    AdminRecentOrdersSection()
                    
                    // MARK: - Loading State
                    
                    if viewModel.isLoading {
                        ProgressView("Loading...")
                            .padding()
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .scrollIndicators(.hidden)
            .navigationTitle("Admin Dashboard")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

// MARK: - Placeholder Sections

struct AdminAnalyticsCardSection: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Analytics Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                AdminMetricCard(title: "Total Sales", value: "$0", icon: "chart.line.uptrend.xyaxis")
                AdminMetricCard(title: "Orders", value: "0", icon: "bag")
                AdminMetricCard(title: "Customers", value: "0", icon: "person.2")
                AdminMetricCard(title: "Products", value: "0", icon: "cube.box")
            }
        }
    }
}

struct AdminSalesOverviewSection: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Sales Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Button("View All") {
                    // TODO: Navigate to sales details
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
                .frame(height: 200)
                .overlay(
                    VStack {
                        Image(systemName: "chart.bar")
                            .font(.largeTitle)
                            .foregroundColor(.gray)
                        Text("Sales Chart Placeholder")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                )
        }
    }
}

struct AdminCustomerInsightsSection: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Customer Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                AdminInsightRow(title: "New Customers Today", value: "0")
                AdminInsightRow(title: "Active Customers", value: "0")
                AdminInsightRow(title: "Customer Retention", value: "0%")
            }
        }
    }
}

struct AdminRecentOrdersSection: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Orders")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Button("View All") {
                    // TODO: Navigate to orders
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            VStack(spacing: 8) {
                ForEach(0..<3, id: \.self) { _ in
                    AdminOrderRow()
                }
            }
        }
    }
}

// MARK: - Helper Components

struct AdminMetricCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                Spacer()
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                    Text(title)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct AdminInsightRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
            Spacer()
            Text(value)
                .font(.body)
                .fontWeight(.semibold)
        }
        .padding(.vertical, 8)
    }
}

struct AdminOrderRow: View {
    var body: some View {
        HStack {
            Circle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 40, height: 40)
                .overlay(
                    Text("--")
                        .font(.caption)
                        .foregroundColor(.gray)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Order #---")
                    .font(.body)
                    .fontWeight(.medium)
                Text("No orders yet")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text("$0.00")
                .font(.body)
                .fontWeight(.semibold)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    AdminHomeView()
        .attachAllEnvironmentObjects()
}
